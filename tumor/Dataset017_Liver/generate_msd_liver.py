#!/usr/bin/env python3
"""
Script to generate msd_liver.json based on dataset.json format
but using files from train_tmp.list and test_tmp.list
"""

import json
import os

def read_file_list(file_path):
    """Read file list and return clean list of filenames"""
    with open(file_path, 'r') as f:
        files = [line.strip() for line in f.readlines() if line.strip()]
    return files

def generate_training_entries(train_files):
    """Generate training entries with proper paths"""
    training_entries = []
    for filename in train_files:
        # Remove .h5 extension and add .nii.gz
        base_name = filename.replace('.h5', '.nii.gz')
        entry = {
            "image": f"./imagesTr/{base_name}",
            "label": f"./labelsTr/{base_name}"
        }
        training_entries.append(entry)
    return training_entries

def generate_test_entries(test_files):
    """Generate test entries with proper paths"""
    test_entries = []
    for filename in test_files:
        # Remove .h5 extension and add .nii.gz
        base_name = filename.replace('.h5', '.nii.gz')
        test_entries.append(f"./imagesTs/{base_name}")
    return test_entries

def main():
    # Read the original dataset.json to get the base structure
    with open('dataset.json', 'r') as f:
        original_data = json.load(f)
    
    # Read train and test file lists
    train_files = read_file_list('train_tmp.list')
    test_files = read_file_list('test_tmp.list')
    
    # Create new dataset structure
    new_data = {
        "name": original_data["name"],
        "description": original_data["description"],
        "reference": original_data["reference"],
        "licence": original_data["licence"],
        "release": original_data["release"],
        "tensorImageSize": original_data["tensorImageSize"],
        "modality": original_data["modality"],
        "labels": original_data["labels"],
        "numTraining": len(train_files),
        "numTest": len(test_files),
        "training": generate_training_entries(train_files),
        "test": generate_test_entries(test_files)
    }
    
    # Write the new JSON file
    with open('msd_liver.json', 'w') as f:
        json.dump(new_data, f, indent=2)
    
    print(f"Generated msd_liver.json with:")
    print(f"  - {len(train_files)} training files")
    print(f"  - {len(test_files)} test files")
    print(f"  - Total: {len(train_files) + len(test_files)} files")

if __name__ == "__main__":
    main()
